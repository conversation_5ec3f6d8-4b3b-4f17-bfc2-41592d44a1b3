package org.galiasystems.csms.cs.adapters.ocpp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.websocket.*;
import org.galiasystems.csms.cs.adapters.ocpp.messages.CallMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.messages.Action;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootNotificationResponse;

import java.net.URI;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.logging.Logger;

import static org.junit.jupiter.api.Assertions.assertEquals;

public abstract class TestCharger extends Endpoint {

    // Constants
    private static final int MESSAGE_TIMEOUT_SECONDS = 5;
    private static final String DEFAULT_SERIAL_NUMBER = "serial01";
    private static final String DEFAULT_MODEL = "model-0";
    private static final String DEFAULT_VENDOR_NAME = "charger-v1";
    private static final String DEFAULT_FIRMWARE_VERSION = "1.0.0";

    // Device information record for cleaner data handling
    public record DeviceInfo(String serialNumber, String model, String vendorName, String firmwareVersion) {
        public static DeviceInfo defaultInfo() {
            return new DeviceInfo(DEFAULT_SERIAL_NUMBER, DEFAULT_MODEL, DEFAULT_VENDOR_NAME, DEFAULT_FIRMWARE_VERSION);
        }
    }

    protected final Logger logger = Logger.getLogger(TestCharger.class.getName());
    protected final LinkedBlockingQueue<String> unprocessedMessages = new LinkedBlockingQueue<>();
    protected final Map<String, ArrayNode> messagesById = new HashMap<>();
    protected final ObjectMapper objectMapper;

    protected boolean isAutoResponse = false;
    protected Session session;
    protected final DeviceInfo deviceInfo;
    protected final long chargingStationId;
    protected final String wsUrl;
    protected final String wsPassword;
    protected Integer heartbeatInterval;

    public TestCharger(long chargingStationId, String wsUrl, String wsPassword) {
        this(chargingStationId, DeviceInfo.defaultInfo(), wsUrl, wsPassword);
    }

    public TestCharger(long chargingStationId, String serialNumber, String model,
                       String vendorName, String firmwareVersion, String wsUrl, String wsPassword) {
        this(chargingStationId, new DeviceInfo(serialNumber, model, vendorName, firmwareVersion), wsUrl, wsPassword);
    }

    public TestCharger(long chargingStationId, DeviceInfo deviceInfo, String wsUrl, String wsPassword) {
        this.chargingStationId = chargingStationId;
        this.deviceInfo = deviceInfo;
        this.wsUrl = wsUrl;
        this.wsPassword = wsPassword;
        this.objectMapper = createObjectMapper();
    }

    public void setAutoResponse(boolean autoResponse) {
        isAutoResponse = autoResponse;
    }

    private static ObjectMapper createObjectMapper() {
        var mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    public abstract ArrayNode createBootNotificationRequest();

    public abstract ArrayNode createHeartbeat();

    public abstract void connect();

    public abstract void handleChangeAvailability(ArrayNode arrayNode);

    public void connect(OCPPVersion ocppVersion) {
        var container = ContainerProvider.getWebSocketContainer();
        try {
            var config = ClientEndpointConfig.Builder.create()
                    .configurator(new TestChargerConfigurator(ocppVersion, String.valueOf(chargingStationId), wsPassword))
                    .build();
            session = container.connectToServer(this, config, new URI(wsUrl));
            logger.info("Charging station " + chargingStationId + " connected successfully");
        } catch (Exception e) {
            var errorMsg = "Failed to connect charging station " + chargingStationId + " to server: " + e.getMessage();
            logger.severe(errorMsg);
            throw new RuntimeException(errorMsg, e);
        }
    }

    public long getChargingStationId() {
        return chargingStationId;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public ArrayNode sendRequest(ArrayNode request) throws InterruptedException, JsonProcessingException {
        var asyncRemote = session.getAsyncRemote();
        var messageId = request.get(1).asText();

        asyncRemote.sendText(request.toString());
        messagesById.put(messageId, request);
        logger.info("Message sent by test charger: " + request);

        var response = unprocessedMessages.poll(MESSAGE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        if (response == null) {
            throw new RuntimeException("No response received within " + MESSAGE_TIMEOUT_SECONDS + " seconds for message: " + messageId);
        }

        return (ArrayNode) objectMapper.readTree(response);
    }

    public <T, A extends Enum<A>> CallMessage<T, A> getNextRequest(Class<T> clazz, Function<String, A> getAction)
            throws InterruptedException, JsonProcessingException {

        var request = unprocessedMessages.poll(MESSAGE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        if (request == null) {
            throw new RuntimeException("No request received within " + MESSAGE_TIMEOUT_SECONDS + " seconds");
        }

        var requestArray = (ArrayNode) objectMapper.readTree(request);
        assertEquals(MessageType.CallMessage.getMessageTypeId(), requestArray.get(0).asInt());

        var messageId = requestArray.get(1).asText();
        var action = getAction.apply(requestArray.get(2).asText());
        var payload = objectMapper.readValue(requestArray.get(3).toString(), clazz);

        return new CallMessage<>(messageId, action, payload);
    }

    public void sendResponse(MessageType messageType, String uuid, Object payload) throws InterruptedException, ExecutionException {
        var response = createMessage(messageType, uuid, null, payload);
        var remoteEndpoint = session.getAsyncRemote();
        var sendOperation = remoteEndpoint.sendText(response.toString());
        sendOperation.get();
        logger.info("Response sent by test client: " + response);
    }

    @Override
    public void onOpen(Session session, EndpointConfig config) {
        session.addMessageHandler(new MessageHandler.Whole<String>() {
            @Override
            public void onMessage(String message) {
                handleIncomingMessage(message);
            }
        });
    }

    private void handleIncomingMessage(String message) {
        logger.info("Message received by test charger: " + message);

        try {
            var arrayNode = (ArrayNode) objectMapper.readTree(message);
            var messageTypeId = arrayNode.get(0).asInt();
            var messageType = MessageType.getMessageTypeById(messageTypeId);

            if (messageType == MessageType.CallMessage) {
                // This is a Request message
                handleRequestMessage(message, arrayNode);
            } else if (messageType == MessageType.CallResultMessage || messageType == MessageType.CallErrorMessage) {
                // This is a Response message - process directly
                handleResponseMessage(message, arrayNode);
            } else {
                logger.warning("Unknown message type: " + messageTypeId);
                unprocessedMessages.add(message);
            }
        } catch (JsonProcessingException e) {
            logger.severe("Failed to parse incoming message: " + message);
            throw new RuntimeException("Message parsing failed", e);
        }
    }

    private void handleRequestMessage(String message, ArrayNode arrayNode) {
        if (isAutoResponse) {
            // Generate and send automatic response
            try {
                generateAndSendAutomaticResponse(arrayNode);
            } catch (Exception e) {
                logger.severe("Failed to generate automatic response: " + e.getMessage());
            }
        } else {
            // Add to unprocessed messages for manual handling
            unprocessedMessages.add(message);
        }
    }

    private void handleResponseMessage(String message, ArrayNode arrayNode) {

        this.unprocessedMessages.add(message);
        var messageId = arrayNode.get(1).asText();
        var originalMessage = messagesById.get(messageId);

        if (isBootNotificationResponse(originalMessage, arrayNode)) {
            handleBootNotificationResponse(arrayNode);
        }
        // Add other response handlers here as needed
    }

    private boolean isBootNotificationResponse(ArrayNode originalMessage, ArrayNode responseMessage) {
        return originalMessage != null &&
                originalMessage.get(2).asText().equals(Action.BootNotification.name());
    }

    private void generateAndSendAutomaticResponse(ArrayNode requestArray) throws Exception {
        var messageId = requestArray.get(1).asText();
        var actionName = requestArray.get(2).asText();

        Object responsePayload = createAutomaticResponsePayload(actionName, requestArray);
        if (responsePayload != null) {
            sendResponse(MessageType.CallResultMessage, messageId, responsePayload);
            logger.info("Automatic response sent for action: " + actionName);
        } else {
            logger.warning("No automatic response available for action: " + actionName);
            throw new UnsupportedOperationException("No automatic response for action: " + actionName);
        }
    }

    private Object createAutomaticResponsePayload(String actionName, ArrayNode requestArray) {
        switch (actionName) {
            case "ChangeAvailability":
                return createChangeAvailabilityResponse();
            case "Reset":
                return createResetResponse();
            case "GetVariables":
                return createGetVariablesResponse(requestArray);
            case "SetVariables":
                return createSetVariablesResponse(requestArray);
            case "RequestStartTransaction":
            case "RemoteStartTransaction":
                return createRequestStartTransactionResponse();
            case "RequestStopTransaction":
            case "RemoteStopTransaction":
                return createRequestStopTransactionResponse();
            default:
                return null; // No automatic response available
        }
    }



    private void handleBootNotificationResponse(ArrayNode arrayNode) {
        try {
            var response = objectMapper.treeToValue(arrayNode.get(2), BootNotificationResponse.class);
            heartbeatInterval = response.interval();
            logger.info("Boot notification response processed, heartbeat interval: " + heartbeatInterval);
        } catch (JsonProcessingException e) {
            logger.severe("Failed to process boot notification response");
            throw new RuntimeException("Boot notification response processing failed", e);
        }
    }

    @Override
    public void onClose(Session session, CloseReason closeReason) {
        logger.info("Connection closed for charging station " + chargingStationId + ": " + closeReason);
    }

    @Override
    public void onError(Session session, Throwable error) {
        var errorMsg = "WebSocket error for charging station " + chargingStationId + ": " + error.getMessage();
        logger.severe(errorMsg);
        // Log stack trace separately for better readability
        if (logger.isLoggable(java.util.logging.Level.FINE)) {
            logger.fine("Stack trace: " + Arrays.toString(error.getStackTrace()));
        }
    }

    // Abstract automatic response creation methods - to be implemented by version-specific subclasses
    protected abstract Object createChangeAvailabilityResponse();

    protected abstract Object createResetResponse();

    protected abstract Object createGetVariablesResponse(ArrayNode requestArray);

    protected abstract Object createSetVariablesResponse(ArrayNode requestArray);

    protected abstract Object createRequestStartTransactionResponse();

    protected abstract Object createRequestStopTransactionResponse();

    public ArrayNode createMessage(MessageType messageType, String uuid, String actionName, Object payload) {
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        var message = objectMapper.createArrayNode();

        message.add(messageType.getMessageTypeId());
        message.add(uuid);
        if (actionName != null) {
            message.add(actionName);
        }
        var payloadJson = (ObjectNode) objectMapper.valueToTree(payload);
        message.add(payloadJson);

        return message;
    }

    public static class TestChargerConfigurator extends ClientEndpointConfig.Configurator {
        private static final String DEFAULT_USERNAME = "test";
        private static final String DEFAULT_PASSWORD = "test";

        private final OCPPVersion ocppVersion;
        private final String userName;
        private final String password;

        public TestChargerConfigurator() {
            this(OCPPVersion.OCPP_1_6, DEFAULT_USERNAME, DEFAULT_PASSWORD);
        }

        public TestChargerConfigurator(OCPPVersion ocppVersion, String userName, String password) {
            this.ocppVersion = ocppVersion;
            this.userName = userName;
            this.password = password;
        }

        @Override
        public void beforeRequest(Map<String, List<String>> headers) {
            super.beforeRequest(headers);

            var credentials = userName + ":" + password;
            var encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

            headers.put("Authorization", List.of("Basic " + encodedCredentials));
            headers.put("Sec-WebSocket-Protocol", List.of(ocppVersion.getVersion()));
        }
    }
}